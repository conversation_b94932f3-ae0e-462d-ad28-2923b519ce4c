# 分类API测试指南

## 问题描述
用户反映 `http://**************:8083/api/client/projects?category=Food` 返回空结果

## 修复内容

### 1. 修复了分类查询逻辑
原来的代码直接用字符串匹配 `category` 字段，现在改为：
1. 首先通过英文名称在 Category 表中查找分类
2. 然后通过 categoryId 查询对应的资源
3. 如果找不到，再尝试中文名称匹配
4. 最后才使用字符串直接匹配（向后兼容）

### 2. 支持的分类名称
根据 DataInitializer，系统支持以下英文分类名称：
- `Houses` (房屋建筑)
- `Treehouses` (树屋小屋)
- `Farmhouses` (农场庄园)
- `Castles` (城堡宫殿)
- `Animals` (动物世界)
- `Plants` (植物花卉)
- `Cartoon` (卡通人物)
- `Vehicles` (交通工具)
- `Food` (食物美食)
- `Holidays` (节日庆典)

## 测试步骤

### 1. 测试基本项目列表API
```bash
# 获取所有项目（不带分类筛选）
curl "http://**************:8083/api/client/projects"

# 获取第一页，每页10个
curl "http://**************:8083/api/client/projects?page=1&size=10"
```

### 2. 测试分类筛选功能
```bash
# 测试英文分类名称
curl "http://**************:8083/api/client/projects?category=Food"
curl "http://**************:8083/api/client/projects?category=Animals"
curl "http://**************:8083/api/client/projects?category=Castles"

# 测试中文分类名称（向后兼容）
curl "http://**************:8083/api/client/projects?category=食物美食"
curl "http://**************:8083/api/client/projects?category=动物世界"
```

### 3. 测试分页功能
```bash
# 获取Food分类的第一页
curl "http://**************:8083/api/client/projects?category=Food&page=1&size=5"

# 获取Food分类的第二页
curl "http://**************:8083/api/client/projects?category=Food&page=2&size=5"
```

### 4. 测试排序功能
```bash
# 按最新排序
curl "http://**************:8083/api/client/projects?category=Food&sort=latest"

# 按热门排序
curl "http://**************:8083/api/client/projects?category=Food&sort=popular"

# 按评分排序
curl "http://**************:8083/api/client/projects?category=Food&sort=rating"

# 按名称排序
curl "http://**************:8083/api/client/projects?category=Food&sort=name"
```

### 5. 测试搜索功能
```bash
# 搜索功能（优先级高于分类筛选）
curl "http://**************:8083/api/client/projects?search=cake"
curl "http://**************:8083/api/client/projects?search=animal"
```

## 预期响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "projects": [
      {
        "id": "1",
        "name": "美味蛋糕",
        "displayName": "美味蛋糕",
        "description": "精美的蛋糕涂色图",
        "category": "Food",
        "difficulty": "简单",
        "thumbnailUrl": "http://**************:8083/uploads/cake.png",
        "previewUrl": "http://**************:8083/uploads/preview/cake.png",
        "downloadUrl": "http://**************:8083/uploads/cake.png",
        "fileSize": 125000,
        "downloadCount": 15,
        "rating": 4.5,
        "releaseDate": "2024-01-15 10:30:00",
        "tags": ["食物", "蛋糕", "甜品"],
        "isPremium": false,
        "isFeatured": true
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalItems": 8,
      "pageSize": 20,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

### 空结果响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "projects": [],
    "pagination": {
      "currentPage": 1,
      "totalPages": 0,
      "totalItems": 0,
      "pageSize": 20,
      "hasNext": false,
      "hasPrevious": false
    }
  }
}
```

## 故障排查

### 1. 如果仍然返回空结果
检查以下几点：
```bash
# 1. 检查分类数据是否正确
curl "http://**************:8083/api/client/categories"

# 2. 检查是否有资源数据
curl "http://**************:8083/api/client/projects"

# 3. 检查分类验证报告
curl "http://**************:8083/api/admin/category-validation/report"
```

### 2. 检查服务器日志
查看应用日志中的以下信息：
- `根据分类名称查询资源: Food`
- `通过英文分类名 Food 找到分类ID: X`
- `通过字符串匹配查询分类: Food`

### 3. 数据修复
如果数据关联有问题，执行数据修复：
```bash
# 访问管理后台
http://**************:8083/admin/categories

# 点击"修复分类数据"按钮
# 或者通过API
curl -X POST "http://**************:8083/admin/fix-category-data"
```

## API参数说明

### `/api/client/projects` 支持的参数：
- `page`: 页码，从1开始，默认1
- `size`: 每页大小，默认20
- `category`: 分类筛选，支持英文和中文名称
- `difficulty`: 难度筛选（暂未实现）
- `search`: 搜索关键词，优先级最高
- `sort`: 排序方式
  - `latest`: 最新（默认）
  - `popular`: 热门（按下载次数）
  - `rating`: 评分
  - `name`: 名称

### 查询优先级：
1. `search` - 如果有搜索关键词，忽略其他筛选条件
2. `category` - 分类筛选
3. 默认查询所有激活资源

## 相关API端点

### 获取分类列表
```bash
curl "http://**************:8083/api/client/categories"
```

### 获取项目详情
```bash
curl "http://**************:8083/api/client/projects/1"
```

### 获取每日推荐
```bash
curl "http://**************:8083/api/client/daily"
```
