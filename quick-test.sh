#!/bin/bash

# 快速测试脚本 - 专门测试用户反映的问题

BASE_URL="http://192.168.110.79:8083"

echo "=== 快速问题验证 ==="
echo ""

# 测试用户反映的具体问题
echo "1. 测试用户反映的问题API"
echo "========================="

echo "测试: $BASE_URL/api/client/projects?category=Food"
response1=$(curl -s "$BASE_URL/api/client/projects?category=Food")
if echo "$response1" | jq . > /dev/null 2>&1; then
    count1=$(echo "$response1" | jq -r '.data.pagination.totalItems // 0')
    projects1=$(echo "$response1" | jq -r '.data.projects | length')
    echo "✓ API响应正常"
    echo "  总项目数: $count1"
    echo "  当前页项目数: $projects1"
    
    if [ "$count1" -gt 0 ]; then
        echo "✓ 找到了Food分类的项目！"
        echo "  项目列表:"
        echo "$response1" | jq -r '.data.projects[] | "  - \(.name) (ID: \(.id))"'
    else
        echo "⚠ Food分类没有找到项目"
    fi
else
    echo "✗ API响应异常"
    echo "响应内容: $response1"
fi

echo ""
echo "测试: $BASE_URL/api/client/projects (获取所有项目)"
response2=$(curl -s "$BASE_URL/api/client/projects")
if echo "$response2" | jq . > /dev/null 2>&1; then
    count2=$(echo "$response2" | jq -r '.data.pagination.totalItems // 0')
    projects2=$(echo "$response2" | jq -r '.data.projects | length')
    echo "✓ API响应正常"
    echo "  总项目数: $count2"
    echo "  当前页项目数: $projects2"
    
    if [ "$count2" -gt 0 ]; then
        echo "✓ 系统中有项目数据"
        echo "  前5个项目:"
        echo "$response2" | jq -r '.data.projects[0:5][] | "  - \(.name) (分类: \(.category), ID: \(.id))"'
    else
        echo "⚠ 系统中没有项目数据"
    fi
else
    echo "✗ API响应异常"
    echo "响应内容: $response2"
fi

echo ""
echo "2. 检查分类数据"
echo "================"

echo "测试: $BASE_URL/api/client/categories"
response3=$(curl -s "$BASE_URL/api/client/categories")
if echo "$response3" | jq . > /dev/null 2>&1; then
    categories=$(echo "$response3" | jq -r '.data.categories | length')
    echo "✓ 分类API响应正常"
    echo "  分类数量: $categories"
    
    if [ "$categories" -gt 0 ]; then
        echo "  分类列表:"
        echo "$response3" | jq -r '.data.categories[] | "  - \(.name) (项目数: \(.projectCount), ID: \(.id))"'
        
        # 检查是否有Food分类
        food_count=$(echo "$response3" | jq -r '.data.categories[] | select(.name == "Food") | .projectCount // 0')
        if [ "$food_count" -gt 0 ]; then
            echo "✓ Food分类存在，包含 $food_count 个项目"
        else
            echo "⚠ Food分类不存在或没有项目"
        fi
    else
        echo "⚠ 没有分类数据"
    fi
else
    echo "✗ 分类API响应异常"
    echo "响应内容: $response3"
fi

echo ""
echo "3. 诊断建议"
echo "============"

# 基于测试结果给出建议
if [ "$count1" -gt 0 ]; then
    echo "✓ 问题已解决！Food分类查询现在可以正常工作了。"
elif [ "$count2" -gt 0 ]; then
    echo "⚠ 系统有数据，但Food分类查询仍有问题。可能的原因："
    echo "  1. 资源没有正确关联到Food分类"
    echo "  2. 分类数据需要修复"
    echo "  建议执行数据修复: $BASE_URL/admin/categories -> 点击'修复分类数据'"
else
    echo "⚠ 系统中没有项目数据。请检查："
    echo "  1. 数据库是否正确初始化"
    echo "  2. 是否有测试数据"
    echo "  3. 数据初始化脚本是否正常执行"
fi

echo ""
echo "4. 快速修复命令"
echo "================"
echo "如果需要修复数据，可以执行："
echo "curl -X POST '$BASE_URL/admin/fix-category-data'"
echo ""
echo "或者访问管理后台："
echo "$BASE_URL/admin/categories"
