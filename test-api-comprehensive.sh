#!/bin/bash

# 综合API测试脚本
# 用于测试修复后的分类查询功能

BASE_URL="http://**************:8083"

echo "=== 涂色App API 综合测试 ==="
echo "基础URL: $BASE_URL"
echo ""

# 颜色输出函数
print_success() {
    echo -e "\033[32m✓ $1\033[0m"
}

print_error() {
    echo -e "\033[31m✗ $1\033[0m"
}

print_info() {
    echo -e "\033[34mℹ $1\033[0m"
}

print_warning() {
    echo -e "\033[33m⚠ $1\033[0m"
}

# 测试API端点的函数
test_api() {
    local url="$1"
    local description="$2"
    local expected_field="$3"
    
    echo "测试: $description"
    echo "URL: $url"
    
    response=$(curl -s "$url")
    
    if [ $? -eq 0 ]; then
        # 检查是否是有效的JSON
        if echo "$response" | jq . > /dev/null 2>&1; then
            success=$(echo "$response" | jq -r '.success // false')
            
            if [ "$success" = "true" ]; then
                if [ -n "$expected_field" ]; then
                    count=$(echo "$response" | jq -r "$expected_field // 0")
                    print_success "成功 - 返回 $count 条记录"
                else
                    print_success "成功"
                fi
            else
                message=$(echo "$response" | jq -r '.message // "未知错误"')
                print_error "API返回失败: $message"
            fi
        else
            print_error "返回的不是有效JSON格式"
            echo "响应内容: $response"
        fi
    else
        print_error "请求失败"
    fi
    echo ""
}

echo "1. 测试基础API端点"
echo "===================="

# 测试获取所有项目
test_api "$BASE_URL/api/client/projects" "获取所有项目" ".data.pagination.totalItems"

# 测试获取分类列表
test_api "$BASE_URL/api/client/categories" "获取分类列表" ".data.categories | length"

echo "2. 测试分类筛选功能"
echo "===================="

# 测试英文分类名称
categories=("Food" "Animals" "Castles" "Houses" "Vehicles")
for category in "${categories[@]}"; do
    test_api "$BASE_URL/api/client/projects?category=$category" "分类筛选: $category" ".data.pagination.totalItems"
done

echo "3. 测试中文分类名称（向后兼容）"
echo "=================================="

# 测试中文分类名称
chinese_categories=("食物美食" "动物世界" "城堡宫殿" "房屋建筑")
for category in "${chinese_categories[@]}"; do
    encoded_category=$(echo "$category" | sed 's/ /%20/g')
    test_api "$BASE_URL/api/client/projects?category=$encoded_category" "分类筛选: $category" ".data.pagination.totalItems"
done

echo "4. 测试难度筛选功能"
echo "===================="

# 测试难度筛选
difficulties=("1" "简单" "easy" "3" "困难" "hard")
for difficulty in "${difficulties[@]}"; do
    test_api "$BASE_URL/api/client/projects?difficulty=$difficulty" "难度筛选: $difficulty" ".data.pagination.totalItems"
done

echo "5. 测试分页功能"
echo "================"

# 测试分页
test_api "$BASE_URL/api/client/projects?page=1&size=5" "分页测试: 第1页，每页5条" ".data.projects | length"
test_api "$BASE_URL/api/client/projects?page=2&size=5" "分页测试: 第2页，每页5条" ".data.projects | length"

echo "6. 测试排序功能"
echo "================"

# 测试排序
sorts=("latest" "popular" "rating" "name")
for sort in "${sorts[@]}"; do
    test_api "$BASE_URL/api/client/projects?sort=$sort&size=3" "排序测试: $sort" ".data.projects | length"
done

echo "7. 测试搜索功能"
echo "================"

# 测试搜索
searches=("cake" "animal" "house")
for search in "${searches[@]}"; do
    test_api "$BASE_URL/api/client/projects?search=$search" "搜索测试: $search" ".data.pagination.totalItems"
done

echo "8. 测试组合查询"
echo "================"

# 测试组合查询
test_api "$BASE_URL/api/client/projects?category=Food&sort=popular&size=3" "组合查询: Food分类，按热门排序" ".data.projects | length"
test_api "$BASE_URL/api/client/projects?category=Animals&difficulty=1&size=5" "组合查询: Animals分类，简单难度" ".data.projects | length"

echo "9. 测试其他API端点"
echo "===================="

# 测试每日推荐
test_api "$BASE_URL/api/client/daily" "每日推荐" ".data.featured | length"

# 测试项目详情（需要先获取一个有效的项目ID）
print_info "获取第一个项目的ID用于详情测试..."
first_project_response=$(curl -s "$BASE_URL/api/client/projects?size=1")
if echo "$first_project_response" | jq . > /dev/null 2>&1; then
    first_project_id=$(echo "$first_project_response" | jq -r '.data.projects[0].id // null')
    if [ "$first_project_id" != "null" ] && [ -n "$first_project_id" ]; then
        test_api "$BASE_URL/api/client/projects/$first_project_id" "项目详情: ID $first_project_id" ".data.id"
    else
        print_warning "没有找到有效的项目ID，跳过详情测试"
    fi
else
    print_warning "无法获取项目列表，跳过详情测试"
fi

echo "10. 数据验证"
echo "============="

# 检查分类数据一致性
print_info "检查分类数据一致性..."
validation_response=$(curl -s "$BASE_URL/api/admin/category-validation/report")
if echo "$validation_response" | jq . > /dev/null 2>&1; then
    total_categories=$(echo "$validation_response" | jq -r '.totalCategories // 0')
    categories_with_english=$(echo "$validation_response" | jq -r '.categoriesWithEnglishName // 0')
    unassigned_resources=$(echo "$validation_response" | jq -r '.unassignedResourcesCount // 0')
    
    print_info "总分类数: $total_categories"
    print_info "有英文名称的分类: $categories_with_english"
    
    if [ "$unassigned_resources" -gt 0 ]; then
        print_warning "未分配分类的资源: $unassigned_resources"
    else
        print_success "所有资源都已正确分类"
    fi
else
    print_warning "无法获取验证报告"
fi

echo ""
echo "=== 测试完成 ==="
echo ""
echo "如果发现问题，请检查："
echo "1. 服务器是否正常运行在 $BASE_URL"
echo "2. 数据库中是否有测试数据"
echo "3. 分类数据是否正确设置了英文名称"
echo "4. 资源数据是否正确关联了分类ID"
echo ""
echo "如需修复数据，请访问: $BASE_URL/admin/categories 并点击'修复分类数据'"
