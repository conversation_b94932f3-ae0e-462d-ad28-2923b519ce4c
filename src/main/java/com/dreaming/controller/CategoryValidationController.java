package com.dreaming.controller;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 分类数据验证控制器
 * 用于检查和验证分类数据的一致性
 */
@RestController
@RequestMapping("/api/admin/category-validation")
@RequiredArgsConstructor
@Slf4j
public class CategoryValidationController {

    private final CategoryRepository categoryRepository;
    private final ColoringResourceRepository resourceRepository;

    /**
     * 获取分类数据验证报告
     */
    @GetMapping("/report")
    public ResponseEntity<Map<String, Object>> getValidationReport() {
        Map<String, Object> report = new HashMap<>();
        
        try {
            // 1. 检查分类基本信息
            List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
            report.put("totalCategories", categories.size());
            
            // 2. 检查英文名称完整性
            long categoriesWithEnglishName = categories.stream()
                .filter(c -> c.getNameEn() != null && !c.getNameEn().trim().isEmpty())
                .count();
            report.put("categoriesWithEnglishName", categoriesWithEnglishName);
            report.put("categoriesWithoutEnglishName", categories.size() - categoriesWithEnglishName);
            
            // 3. 分类资源统计
            Map<String, Object> categoryStats = new HashMap<>();
            for (Category category : categories) {
                Long resourceCount = resourceRepository.countByCategoryIdAndActiveTrue(category.getId());
                Map<String, Object> categoryInfo = new HashMap<>();
                categoryInfo.put("chineseName", category.getName());
                categoryInfo.put("englishName", category.getNameEn());
                categoryInfo.put("resourceCount", resourceCount);
                categoryInfo.put("isActive", category.getIsActive());
                categoryStats.put(category.getId().toString(), categoryInfo);
            }
            report.put("categoryStats", categoryStats);
            
            // 4. 检查未分类资源
            List<ColoringResource> unassignedResources = resourceRepository.findByCategoryIdIsNullAndActiveTrue();
            report.put("unassignedResourcesCount", unassignedResources.size());
            
            // 5. 检查可能的分类错误
            Map<String, Object> potentialIssues = new HashMap<>();
            
            // 检查名称中包含特定关键词但分类可能不正确的资源
            List<ColoringResource> allResources = resourceRepository.findByActiveTrueOrderBySortOrderAsc();
            int castleInWrongCategory = 0;
            int animalInWrongCategory = 0;
            int foodInWrongCategory = 0;
            
            Category castleCategory = categoryRepository.findByNameEn("Castles");
            Category animalCategory = categoryRepository.findByNameEn("Animals");
            Category foodCategory = categoryRepository.findByNameEn("Food");
            
            for (ColoringResource resource : allResources) {
                String name = resource.getName().toLowerCase();
                
                // 检查城堡相关资源
                if ((name.contains("castle") || name.contains("城堡")) && 
                    castleCategory != null && !castleCategory.getId().equals(resource.getCategoryId())) {
                    castleInWrongCategory++;
                }
                
                // 检查动物相关资源
                if ((name.contains("animal") || name.contains("动物")) && 
                    animalCategory != null && !animalCategory.getId().equals(resource.getCategoryId())) {
                    animalInWrongCategory++;
                }
                
                // 检查食物相关资源
                if ((name.contains("food") || name.contains("食物")) && 
                    foodCategory != null && !foodCategory.getId().equals(resource.getCategoryId())) {
                    foodInWrongCategory++;
                }
            }
            
            potentialIssues.put("castleInWrongCategory", castleInWrongCategory);
            potentialIssues.put("animalInWrongCategory", animalInWrongCategory);
            potentialIssues.put("foodInWrongCategory", foodInWrongCategory);
            report.put("potentialIssues", potentialIssues);
            
            // 6. 生成建议
            Map<String, String> recommendations = new HashMap<>();
            if (categoriesWithoutEnglishName > 0) {
                recommendations.put("englishNames", "有 " + (categories.size() - categoriesWithEnglishName) + " 个分类缺少英文名称，建议补充");
            }
            if (unassignedResources.size() > 0) {
                recommendations.put("unassignedResources", "有 " + unassignedResources.size() + " 个资源未分配分类，需要处理");
            }
            if (castleInWrongCategory > 0 || animalInWrongCategory > 0 || foodInWrongCategory > 0) {
                recommendations.put("wrongCategories", "检测到可能的分类错误，建议运行数据修复");
            }
            report.put("recommendations", recommendations);
            
            report.put("status", "success");
            report.put("message", "验证报告生成成功");
            
        } catch (Exception e) {
            log.error("生成分类验证报告失败", e);
            report.put("status", "error");
            report.put("message", "生成验证报告失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(report);
    }

    /**
     * 获取详细的分类资源列表
     */
    @GetMapping("/detailed-stats")
    public ResponseEntity<Map<String, Object>> getDetailedStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
            
            for (Category category : categories) {
                List<ColoringResource> resources = resourceRepository.findByCategoryIdAndActiveTrue(category.getId());
                
                Map<String, Object> categoryDetail = new HashMap<>();
                categoryDetail.put("id", category.getId());
                categoryDetail.put("chineseName", category.getName());
                categoryDetail.put("englishName", category.getNameEn());
                categoryDetail.put("resourceCount", resources.size());
                categoryDetail.put("resources", resources.stream()
                    .map(r -> Map.of(
                        "id", r.getId(),
                        "name", r.getName(),
                        "category", r.getCategory(),
                        "categoryId", r.getCategoryId()
                    ))
                    .toList());
                
                stats.put(category.getNameEn() != null ? category.getNameEn() : category.getName(), categoryDetail);
            }
            
        } catch (Exception e) {
            log.error("获取详细统计信息失败", e);
            stats.put("error", "获取详细统计信息失败: " + e.getMessage());
        }
        
        return ResponseEntity.ok(stats);
    }
}
