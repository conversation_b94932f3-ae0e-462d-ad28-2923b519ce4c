package com.dreaming.repository;

import com.dreaming.entity.ColoringResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 涂色资源数据访问层
 */
@Repository
public interface ColoringResourceRepository extends JpaRepository<ColoringResource, Long> {

    /**
     * 根据分类查找激活的资源 (字符串分类，向后兼容)
     */
    List<ColoringResource> findByCategoryAndActiveTrue(String category);

    /**
     * 根据分类ID查找激活的资源 (新增)
     */
    List<ColoringResource> findByCategoryIdAndActiveTrue(Long categoryId);

    /**
     * 客户端API专用查询方法
     */

    /**
     * 分页查询激活的资源
     */
    Page<ColoringResource> findByActiveTrue(Pageable pageable);

    /**
     * 根据ID查找激活的资源
     */
    Optional<ColoringResource> findByIdAndActiveTrue(Long id);

    /**
     * 根据名称搜索激活的资源
     */
    Page<ColoringResource> findByNameContainingIgnoreCaseAndActiveTrue(String name, Pageable pageable);

    /**
     * 根据难度查找激活的资源
     */
    List<ColoringResource> findByDifficultyAndActiveTrue(Integer difficulty);

    /**
     * 统计分类下的激活资源数量
     */
    Long countByCategoryIdAndActiveTrue(Long categoryId);

    /**
     * 根据版本查找资源
     */
    List<ColoringResource> findByVersionAndActiveTrue(String version);

    /**
     * 根据分类分页查询激活的资源
     */
    Page<ColoringResource> findByCategoryAndActiveTrue(String category, Pageable pageable);

    /**
     * 根据名称模糊查询激活的资源
     */
    @Query("SELECT r FROM ColoringResource r WHERE r.name LIKE %:name% AND r.active = true")
    List<ColoringResource> findByNameContainingAndActiveTrue(@Param("name") String name);

    /**
     * 获取所有分类
     */
    @Query("SELECT DISTINCT r.category FROM ColoringResource r WHERE r.active = true ORDER BY r.category")
    List<String> findDistinctCategories();

    /**
     * 获取所有激活的资源
     */
    List<ColoringResource> findByActiveTrue();

    /**
     * 根据排序顺序获取激活的资源
     */
    List<ColoringResource> findByActiveTrueOrderBySortOrderAsc();

    /**
     * 获取热门资源(按下载次数排序)
     */
    @Query("SELECT r FROM ColoringResource r WHERE r.active = true ORDER BY r.downloadCount DESC")
    List<ColoringResource> findPopularResources(Pageable pageable);

    /**
     * 获取最新资源
     */
    @Query("SELECT r FROM ColoringResource r WHERE r.active = true ORDER BY r.createdAt DESC")
    List<ColoringResource> findLatestResources(Pageable pageable);

    /**
     * 统计激活资源总数
     */
    long countByActiveTrue();

    /**
     * 根据版本统计资源数量
     */
    long countByVersionAndActiveTrue(String version);

    /**
     * 检查指定存储文件名的资源是否存在
     */
    boolean existsByStoredFilename(String storedFilename);

    /**
     * 根据名称查找激活的资源
     */
    List<ColoringResource> findByNameAndActiveTrue(String name);

    /**
     * 查找未分配分类ID的激活资源
     */
    List<ColoringResource> findByCategoryIdIsNullAndActiveTrue();
}
