package com.dreaming.service;

import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 分类数据修复服务
 * 用于修复资源分类关联错误和确保英文名称一致性
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryDataFixService {

    private final CategoryRepository categoryRepository;
    private final ColoringResourceRepository resourceRepository;

    /**
     * 修复所有分类数据问题
     */
    @Transactional
    public void fixAllCategoryData() {
        log.info("开始修复分类数据...");
        
        // 1. 确保分类表有正确的英文名称
        ensureCategoryEnglishNames();
        
        // 2. 修复资源分类关联
        fixResourceCategoryAssociations();
        
        // 3. 验证修复结果
        validateFixResults();
        
        log.info("分类数据修复完成");
    }

    /**
     * 确保分类表有正确的英文名称
     */
    private void ensureCategoryEnglishNames() {
        log.info("检查并修复分类英文名称...");
        
        // 预定义的中文到英文映射
        Map<String, String> nameMapping = new HashMap<>();
        nameMapping.put("房屋建筑", "Houses");
        nameMapping.put("树屋小屋", "Treehouses");
        nameMapping.put("农场庄园", "Farmhouses");
        nameMapping.put("城堡宫殿", "Castles");
        nameMapping.put("动物世界", "Animals");
        nameMapping.put("植物花卉", "Plants");
        nameMapping.put("卡通人物", "Cartoon");
        nameMapping.put("交通工具", "Vehicles");
        nameMapping.put("食物美食", "Food");
        nameMapping.put("节日庆典", "Holidays");

        List<Category> categories = categoryRepository.findAll();
        for (Category category : categories) {
            String expectedEnglishName = nameMapping.get(category.getName());
            if (expectedEnglishName != null && 
                (category.getNameEn() == null || !category.getNameEn().equals(expectedEnglishName))) {
                
                log.info("修复分类 {} 的英文名称: {} -> {}", 
                    category.getName(), category.getNameEn(), expectedEnglishName);
                category.setNameEn(expectedEnglishName);
                categoryRepository.save(category);
            }
        }
    }

    /**
     * 修复资源分类关联
     */
    private void fixResourceCategoryAssociations() {
        log.info("修复资源分类关联...");
        
        List<ColoringResource> resources = resourceRepository.findAll();
        
        for (ColoringResource resource : resources) {
            // 根据资源名称智能推断正确的分类
            Long correctCategoryId = inferCorrectCategory(resource);
            
            if (correctCategoryId != null && !correctCategoryId.equals(resource.getCategoryId())) {
                Category correctCategory = categoryRepository.findById(correctCategoryId).orElse(null);
                if (correctCategory != null) {
                    log.info("修复资源 {} 的分类关联: {} -> {} ({})", 
                        resource.getName(), 
                        resource.getCategoryId(), 
                        correctCategoryId, 
                        correctCategory.getNameEn());
                    
                    resource.setCategoryId(correctCategoryId);
                    resource.setCategory(correctCategory.getNameEn()); // 同时更新字符串字段
                    resourceRepository.save(resource);
                }
            }
        }
    }

    /**
     * 根据资源名称智能推断正确的分类
     */
    private Long inferCorrectCategory(ColoringResource resource) {
        String name = resource.getName().toLowerCase();
        
        // 城堡相关
        if (name.contains("castle") || name.contains("palace") || name.contains("城堡") || name.contains("宫殿")) {
            return getCategoryIdByEnglishName("Castles");
        }
        
        // 动物相关
        if (name.contains("animal") || name.contains("cat") || name.contains("dog") || name.contains("bird") ||
            name.contains("动物") || name.contains("猫") || name.contains("狗") || name.contains("鸟")) {
            return getCategoryIdByEnglishName("Animals");
        }
        
        // 食物相关
        if (name.contains("food") || name.contains("cake") || name.contains("fruit") || name.contains("drink") ||
            name.contains("食物") || name.contains("蛋糕") || name.contains("水果") || name.contains("饮料")) {
            return getCategoryIdByEnglishName("Food");
        }
        
        // 房屋建筑相关
        if (name.contains("house") || name.contains("building") || name.contains("home") ||
            name.contains("房") || name.contains("屋") || name.contains("建筑")) {
            return getCategoryIdByEnglishName("Houses");
        }
        
        // 交通工具相关
        if (name.contains("car") || name.contains("vehicle") || name.contains("truck") || name.contains("plane") ||
            name.contains("汽车") || name.contains("车") || name.contains("飞机") || name.contains("交通")) {
            return getCategoryIdByEnglishName("Vehicles");
        }
        
        // 卡通人物相关
        if (name.contains("cartoon") || name.contains("character") || name.contains("人物") || name.contains("卡通")) {
            return getCategoryIdByEnglishName("Cartoon");
        }
        
        // 植物花卉相关
        if (name.contains("flower") || name.contains("plant") || name.contains("tree") ||
            name.contains("花") || name.contains("植物") || name.contains("树")) {
            return getCategoryIdByEnglishName("Plants");
        }
        
        // Fantasy类型的资源应该根据具体内容分类，如果无法确定，默认分到Houses
        if (name.contains("fantasy")) {
            // 可以根据更具体的关键词进一步分类
            return getCategoryIdByEnglishName("Houses"); // 默认分类
        }
        
        // 如果无法确定，保持原有分类
        return resource.getCategoryId();
    }

    /**
     * 根据英文名称获取分类ID
     */
    private Long getCategoryIdByEnglishName(String englishName) {
        Category category = categoryRepository.findByNameEn(englishName);
        return category != null ? category.getId() : null;
    }

    /**
     * 验证修复结果
     */
    private void validateFixResults() {
        log.info("验证修复结果...");
        
        // 检查分类数据
        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
        log.info("当前激活分类数量: {}", categories.size());
        
        for (Category category : categories) {
            Long resourceCount = resourceRepository.countByCategoryIdAndActiveTrue(category.getId());
            log.info("分类: {} ({}) - 资源数量: {}", 
                category.getNameEn(), category.getName(), resourceCount);
        }
        
        // 检查是否还有未分类的资源
        List<ColoringResource> unassignedResources = resourceRepository.findByCategoryIdIsNullAndActiveTrue();
        if (!unassignedResources.isEmpty()) {
            log.warn("发现 {} 个未分配分类的资源", unassignedResources.size());
            for (ColoringResource resource : unassignedResources) {
                log.warn("未分类资源: {}", resource.getName());
            }
        }
    }

    /**
     * 获取分类统计信息
     */
    public void printCategoryStatistics() {
        log.info("=== 分类统计信息 ===");
        
        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();
        
        for (Category category : categories) {
            Long resourceCount = resourceRepository.countByCategoryIdAndActiveTrue(category.getId());
            System.out.printf("%s %d (%s)%n", 
                category.getNameEn(), resourceCount, category.getName());
        }
    }
}
