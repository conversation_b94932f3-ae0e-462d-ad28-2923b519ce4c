package com.dreaming.service.impl;

import com.dreaming.dto.*;
import com.dreaming.entity.Category;
import com.dreaming.entity.ColoringResource;
import com.dreaming.repository.CategoryRepository;
import com.dreaming.repository.ColoringResourceRepository;
import com.dreaming.service.ClientApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户端API服务实现
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class ClientApiServiceImpl implements ClientApiService {

    private final ColoringResourceRepository resourceRepository;
    private final CategoryRepository categoryRepository;

    @Override
    public ProjectListResponse getProjects(Integer page, Integer size, String category, 
                                         String difficulty, String search, String sort) {
        log.info("获取项目列表: page={}, size={}, category={}, difficulty={}, search={}, sort={}", 
                page, size, category, difficulty, search, sort);

        // 创建分页对象
        Pageable pageable = createPageable(page, size, sort);
        
        // 查询数据
        Page<ColoringResource> resourcePage;
        
        if (search != null && !search.trim().isEmpty()) {
            // 搜索查询
            resourcePage = resourceRepository.findByNameContainingIgnoreCaseAndActiveTrue(search.trim(), pageable);
        } else if (category != null && !category.trim().isEmpty()) {
            // 分类查询 - 支持英文分类名称
            resourcePage = getResourcesByCategory(category.trim(), pageable);
        } else if (difficulty != null && !difficulty.trim().isEmpty()) {
            // 难度查询
            resourcePage = getResourcesByDifficulty(difficulty.trim(), pageable);
        } else {
            // 全部查询
            resourcePage = resourceRepository.findByActiveTrue(pageable);
        }

        // 转换为DTO
        List<ProjectDTO> projects = resourcePage.getContent().stream()
                .map(this::convertToProjectDTO)
                .collect(Collectors.toList());

        // 创建分页信息
        PaginationInfo pagination = new PaginationInfo(
                page,
                resourcePage.getTotalPages(),
                (int) resourcePage.getTotalElements(),
                size,
                resourcePage.hasNext(),
                resourcePage.hasPrevious()
        );

        return new ProjectListResponse(projects, pagination);
    }

    @Override
    public ProjectDTO getProjectDetail(String id) {
        log.info("获取项目详情: id={}", id);
        
        try {
            Long resourceId = Long.parseLong(id);
            Optional<ColoringResource> resourceOpt = resourceRepository.findByIdAndActiveTrue(resourceId);
            
            if (resourceOpt.isPresent()) {
                ColoringResource resource = resourceOpt.get();
                // 增加下载计数（查看详情也算一次访问）
                resource.setDownloadCount(resource.getDownloadCount() + 1);
                resourceRepository.save(resource);
                
                return convertToProjectDTO(resource);
            }
        } catch (NumberFormatException e) {
            log.warn("无效的项目ID: {}", id);
        }
        
        return null;
    }

    @Override
    public CategoriesResponse getCategories() {
        log.info("获取分类列表");

        List<Category> categories = categoryRepository.findByIsActiveTrueOrderBySortOrderAsc();

        List<CategoryWithCountDTO> categoryDTOs = categories.stream()
                .map(category -> {
                    // 计算每个分类的项目数量
                    Long count = resourceRepository.countByCategoryIdAndActiveTrue(category.getId());

                    // 优先使用英文名称以便于客户端通信
                    String categoryName = category.getNameEn() != null && !category.getNameEn().trim().isEmpty()
                                         ? category.getNameEn() : category.getName();

                    return new CategoryWithCountDTO(
                            category.getId().toString(),
                            categoryName,
                            category.getDescription(),
                            category.getIconUrl(),
                            count,
                            category.getSortOrder()
                    );
                })
                .collect(Collectors.toList());

        return new CategoriesResponse(categoryDTOs);
    }

    @Override
    public DailyResponse getDailyRecommendations() {
        log.info("获取每日推荐");
        
        // 获取今日挑战（随机选择一个中等难度的项目）
        TodayChallengeDTO todayChallenge = getTodayChallenge();
        
        // 获取精选项目（评分高的项目）
        List<FeaturedProjectDTO> featuredProjects = getFeaturedProjects();
        
        // 获取热门项目（下载量高的项目）
        List<TrendingProjectDTO> trendingProjects = getTrendingProjects();
        
        return new DailyResponse(todayChallenge, featuredProjects, trendingProjects);
    }

    @Override
    public UpdateResponse checkUpdates(String currentVersion, String lastContentUpdate) {
        log.info("检查更新: currentVersion={}, lastContentUpdate={}", currentVersion, lastContentUpdate);
        
        // 检查应用更新（这里简化处理，实际应该从配置或数据库读取）
        AppUpdateDTO appUpdate = checkAppUpdate(currentVersion);
        
        // 检查内容更新
        ContentUpdateInfo contentUpdates = checkContentUpdates(lastContentUpdate);
        
        boolean hasUpdates = (appUpdate != null) || 
                           (contentUpdates.getNewProjectsCount() > 0 || contentUpdates.getUpdatedProjectsCount() > 0);
        
        return new UpdateResponse(hasUpdates, appUpdate, contentUpdates);
    }

    @Override
    @Transactional
    public void recordDownload(String id) {
        log.info("记录下载: id={}", id);
        
        try {
            Long resourceId = Long.parseLong(id);
            Optional<ColoringResource> resourceOpt = resourceRepository.findById(resourceId);
            
            if (resourceOpt.isPresent()) {
                ColoringResource resource = resourceOpt.get();
                resource.setDownloadCount(resource.getDownloadCount() + 1);
                resourceRepository.save(resource);
                log.info("下载计数更新成功: id={}, count={}", id, resource.getDownloadCount());
            }
        } catch (NumberFormatException e) {
            log.warn("无效的项目ID: {}", id);
        }
    }

    /**
     * 创建分页对象
     */
    private Pageable createPageable(Integer page, Integer size, String sort) {
        Sort sortObj;
        switch (sort) {
            case "popular":
                sortObj = Sort.by(Sort.Direction.DESC, "downloadCount");
                break;
            case "rating":
                sortObj = Sort.by(Sort.Direction.DESC, "rating");
                break;
            case "name":
                sortObj = Sort.by(Sort.Direction.ASC, "name");
                break;
            case "latest":
            default:
                sortObj = Sort.by(Sort.Direction.DESC, "createdAt");
                break;
        }
        
        return PageRequest.of(page - 1, size, sortObj);
    }

    /**
     * 根据分类名称获取资源（支持英文分类名称）
     */
    private Page<ColoringResource> getResourcesByCategory(String categoryName, Pageable pageable) {
        log.info("根据分类名称查询资源: {}", categoryName);

        // 首先尝试通过英文名称查找分类
        Category category = categoryRepository.findByNameEn(categoryName);
        if (category != null) {
            log.info("通过英文分类名 {} 找到分类ID: {}", categoryName, category.getId());
            // 使用分类ID查找资源（转换为分页查询）
            List<ColoringResource> resources = resourceRepository.findByCategoryIdAndActiveTrue(category.getId());
            return convertListToPage(resources, pageable);
        }

        // 如果没有找到英文分类，尝试通过中文名称查找
        category = categoryRepository.findByName(categoryName);
        if (category != null) {
            log.info("通过中文分类名 {} 找到分类ID: {}", categoryName, category.getId());
            List<ColoringResource> resources = resourceRepository.findByCategoryIdAndActiveTrue(category.getId());
            return convertListToPage(resources, pageable);
        }

        // 最后尝试直接用字符串匹配（向后兼容）
        log.info("通过字符串匹配查询分类: {}", categoryName);
        return resourceRepository.findByCategoryAndActiveTrue(categoryName, pageable);
    }

    /**
     * 根据难度获取资源
     */
    private Page<ColoringResource> getResourcesByDifficulty(String difficultyStr, Pageable pageable) {
        log.info("根据难度查询资源: {}", difficultyStr);

        try {
            Integer difficulty = parseDifficulty(difficultyStr);
            if (difficulty != null) {
                List<ColoringResource> resources = resourceRepository.findByDifficultyAndActiveTrue(difficulty);
                return convertListToPage(resources, pageable);
            }
        } catch (Exception e) {
            log.warn("解析难度参数失败: {}", difficultyStr, e);
        }

        // 如果解析失败，返回空结果
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }

    /**
     * 解析难度参数
     */
    private Integer parseDifficulty(String difficultyStr) {
        // 支持数字和中文难度描述
        switch (difficultyStr.toLowerCase()) {
            case "1":
            case "简单":
            case "easy":
                return 1;
            case "2":
            case "普通":
            case "normal":
                return 2;
            case "3":
            case "中等":
            case "medium":
                return 3;
            case "4":
            case "困难":
            case "hard":
                return 4;
            case "5":
            case "专家":
            case "expert":
                return 5;
            default:
                try {
                    return Integer.parseInt(difficultyStr);
                } catch (NumberFormatException e) {
                    return null;
                }
        }
    }

    /**
     * 将List转换为Page对象（用于分页）
     */
    private Page<ColoringResource> convertListToPage(List<ColoringResource> resources, Pageable pageable) {
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), resources.size());

        List<ColoringResource> pageContent = resources.subList(start, end);
        return new PageImpl<>(pageContent, pageable, resources.size());
    }

    /**
     * 转换为项目DTO
     */
    private ProjectDTO convertToProjectDTO(ColoringResource resource) {
        ProjectDTO dto = new ProjectDTO();
        dto.setId(resource.getId().toString());
        dto.setName(resource.getName());
        dto.setDisplayName(resource.getName());
        dto.setDescription(resource.getDescription());
        dto.setCategory(resource.getCategory());
        dto.setDifficulty(getDifficultyText(resource.getDifficulty()));
        dto.setTotalRegions(resource.getRegionsCount());
        dto.setTotalColors(resource.getColorsCount());
        dto.setEstimatedTimeMinutes(resource.getEstimatedTime());
        dto.setVersion(resource.getVersion());
        dto.setFileSize(resource.getFileSize());
        dto.setThumbnailUrl(getFileUrl(resource.getStoredFilename(), resource.getCategory()));
        dto.setPreviewUrl(resource.getPreviewImageUrl());
        dto.setTags(parseTags(resource.getTags()));
        dto.setReleaseDate(formatDateTime(resource.getCreatedAt()));
        dto.setPopularity(calculatePopularity(resource));
        dto.setRating(resource.getRating());
        dto.setDownloadCount(resource.getDownloadCount());
        
        // 设置文件信息
        ProjectFilesDTO files = new ProjectFilesDTO();

        // 确保JSON URL不为null
        String jsonUrl = resource.getColoringDataUrl();
        if (jsonUrl == null || jsonUrl.trim().isEmpty()) {
            // 如果没有专门的JSON文件，生成一个默认的JSON URL
            String jsonFilename = resource.getStoredFilename().replaceAll("\\.[^.]+$", ".json");
            jsonUrl = getFileUrl(jsonFilename, resource.getCategory());
        }
        files.setJsonUrl(jsonUrl);

        // 确保outline URL不为null
        String outlineUrl = getFileUrl(resource.getStoredFilename(), resource.getCategory());
        files.setOutlineUrl(outlineUrl);

        files.setJsonSize(0L); // 需要实际计算
        files.setOutlineSize(resource.getFileSize() != null ? resource.getFileSize() : 0L);
        files.setJsonChecksum(null);
        files.setOutlineChecksum(null);

        dto.setFiles(files);
        
        return dto;
    }

    /**
     * 获取今日挑战
     */
    private TodayChallengeDTO getTodayChallenge() {
        // 使用当前日期作为种子，确保每天的挑战是固定的
        Random random = new Random(LocalDateTime.now().getDayOfYear());
        
        List<ColoringResource> resources = resourceRepository.findByDifficultyAndActiveTrue(3);
        if (!resources.isEmpty()) {
            ColoringResource resource = resources.get(random.nextInt(resources.size()));
            
            TodayChallengeDTO challenge = new TodayChallengeDTO();
            challenge.setId(resource.getId().toString());
            challenge.setName(resource.getName());
            challenge.setDisplayName(resource.getName());
            challenge.setDescription(resource.getDescription());
            challenge.setCategory(resource.getCategory());
            challenge.setDifficulty(getDifficultyText(resource.getDifficulty()));
            challenge.setTotalRegions(resource.getRegionsCount());
            challenge.setEstimatedTimeMinutes(resource.getEstimatedTime());
            challenge.setThumbnailUrl(getFileUrl(resource.getStoredFilename()));
            challenge.setPreviewUrl(resource.getPreviewImageUrl());
            challenge.setTags(parseTags(resource.getTags()));
            challenge.setChallengeReason("今日为您精选的中等难度挑战，适合提升涂色技巧！");
            
            return challenge;
        }
        
        return null;
    }

    /**
     * 获取精选项目
     */
    private List<FeaturedProjectDTO> getFeaturedProjects() {
        Pageable pageable = PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "rating"));
        Page<ColoringResource> resources = resourceRepository.findByActiveTrue(pageable);
        
        return resources.getContent().stream()
                .map(resource -> {
                    FeaturedProjectDTO featured = new FeaturedProjectDTO();
                    featured.setId(resource.getId().toString());
                    featured.setName(resource.getName());
                    featured.setDisplayName(resource.getName());
                    featured.setDescription(resource.getDescription());
                    featured.setCategory(resource.getCategory());
                    featured.setDifficulty(getDifficultyText(resource.getDifficulty()));
                    featured.setThumbnailUrl(getFileUrl(resource.getStoredFilename()));
                    featured.setPreviewUrl(resource.getPreviewImageUrl());
                    featured.setDownloadCount(resource.getDownloadCount());
                    featured.setRating(resource.getRating());
                    featured.setFeaturedReason("高评分精选作品");
                    return featured;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取热门项目
     */
    private List<TrendingProjectDTO> getTrendingProjects() {
        Pageable pageable = PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "downloadCount"));
        Page<ColoringResource> resources = resourceRepository.findByActiveTrue(pageable);
        
        return resources.getContent().stream()
                .map(resource -> {
                    TrendingProjectDTO trending = new TrendingProjectDTO();
                    trending.setId(resource.getId().toString());
                    trending.setName(resource.getName());
                    trending.setDisplayName(resource.getName());
                    trending.setDescription(resource.getDescription());
                    trending.setCategory(resource.getCategory());
                    trending.setDifficulty(getDifficultyText(resource.getDifficulty()));
                    trending.setThumbnailUrl(getFileUrl(resource.getStoredFilename()));
                    trending.setPreviewUrl(resource.getPreviewImageUrl());
                    trending.setDownloadCount(resource.getDownloadCount());
                    trending.setReleaseDate(formatDateTime(resource.getCreatedAt()));
                    trending.setTrendingReason("热门下载");
                    return trending;
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查应用更新
     */
    private AppUpdateDTO checkAppUpdate(String currentVersion) {
        // 这里简化处理，实际应该从配置文件或数据库读取最新版本信息
        String latestVersion = "1.1.0";
        
        if (!latestVersion.equals(currentVersion)) {
            AppUpdateDTO update = new AppUpdateDTO();
            update.setLatestVersion(latestVersion);
            update.setCurrentVersion(currentVersion);
            update.setDownloadUrl("https://example.com/app-latest.apk");
            update.setReleaseNotes("修复已知问题，新增批量下载功能");
            update.setForceUpdate(false);
            update.setReleaseDate(LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE));
            update.setFileSize(25 * 1024 * 1024L); // 25MB
            update.setUpdateType("minor");
            return update;
        }
        
        return null;
    }

    /**
     * 检查内容更新
     */
    private ContentUpdateInfo checkContentUpdates(String lastContentUpdate) {
        // 简化处理，实际应该根据lastContentUpdate时间查询新增和更新的内容
        ContentUpdateInfo info = new ContentUpdateInfo();
        info.setNewProjectsCount(0);
        info.setUpdatedProjectsCount(0);
        info.setLastContentUpdate(LocalDateTime.now());
        info.setUpdateSummary("暂无新内容更新");
        
        return info;
    }

    /**
     * 辅助方法
     */
    private String getDifficultyText(Integer difficulty) {
        if (difficulty == null) return "未知";
        switch (difficulty) {
            case 1: return "非常简单";
            case 2: return "简单";
            case 3: return "中等";
            case 4: return "困难";
            case 5: return "非常困难";
            default: return "未知";
        }
    }

    private String getFileUrl(String filename) {
        if (filename == null) return null;
        // 检查filename是否已经包含路径
        if (filename.contains("/")) {
            return "/api/files/" + filename;
        } else {
            // 如果只是文件名，需要添加分类路径
            return "/api/files/" + filename;
        }
    }

    /**
     * 获取带分类的文件URL
     */
    private String getFileUrl(String filename, String category) {
        if (filename == null) return null;
        if (category == null) category = "default";

        // 检查filename是否已经包含路径
        if (filename.contains("/")) {
            return "/api/files/" + filename;
        } else {
            // 添加分类路径
            return "/api/files/" + category + "/" + filename;
        }
    }

    private List<String> parseTags(String tagsJson) {
        // 简化处理，实际应该解析JSON
        if (tagsJson == null || tagsJson.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return Arrays.asList(tagsJson.split(","));
    }

    private String formatDateTime(LocalDateTime dateTime) {
        if (dateTime == null) return null;
        return dateTime.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    private Integer calculatePopularity(ColoringResource resource) {
        // 简单的流行度计算：下载量 + 评分*10
        long downloads = resource.getDownloadCount() != null ? resource.getDownloadCount() : 0;
        double rating = resource.getRating() != null ? resource.getRating() : 0.0;
        return (int) (downloads + rating * 10);
    }
}
