<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>资源上传 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-upload"></i>
                资源上传
            </h2>
            <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                资源列表
            </a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    批量上传资源
                </h5>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                    
                    <!-- 文件选择区域 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-images"></i>
                            选择文件 <span class="text-danger">*</span>
                        </label>
                        <div class="file-drop-zone" id="dropZone">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持图片文件(JPG, PNG, GIF)和JSON配置文件</p>
                            <input type="file" name="files" id="filesInput" class="d-none" 
                                   accept="image/*,.json" multiple>
                            <input type="file" id="folderInput" class="d-none" 
                                   webkitdirectory directory multiple>
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="button" class="btn btn-primary" id="selectFilesBtn">
                                    <i class="fas fa-file"></i>
                                    选择文件
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="selectFolderBtn">
                                    <i class="fas fa-folder"></i>
                                    选择文件夹
                                </button>
                            </div>
                        </div>
                        
                        <!-- 文件列表 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类
                        </label>
                        
                        <!-- 自动分类选项 -->
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="autoCategory" 
                                   name="autoCategory" onchange="toggleCategoryMode()">
                            <label class="form-check-label" for="autoCategory">
                                <i class="fas fa-magic"></i>
                                根据文件名自动分类
                            </label>
                        </div>

                        <!-- 手动分类选择 -->
                        <div id="manualCategorySection">
                            <div th:if="${categoryDTOs != null and !#lists.isEmpty(categoryDTOs)}">
                                <select class="form-select" name="defaultCategoryId" id="categorySelect" required>
                                    <option value="">请选择分类</option>
                                    <option th:each="category : ${categoryDTOs}" 
                                            th:value="${category.id}" 
                                            th:text="${category.name}">分类名称</option>
                                </select>
                            </div>
                            
                            <!-- 降级方案 -->
                            <div th:if="${categoryDTOs == null or #lists.isEmpty(categoryDTOs)}">
                                <select class="form-select" name="defaultCategory" required>
                                    <option value="">请选择分类</option>
                                    <option th:each="category : ${categories}" th:value="${category}" th:text="${category}">分类</option>
                                </select>
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    请先在 <a th:href="@{/admin/categories}">分类管理</a> 中创建分类
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label for="difficulty" class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" id="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2" selected>⭐⭐ 普通</option>
                                <option value="3">⭐⭐⭐ 困难</option>
                                <option value="4">⭐⭐⭐⭐ 专家</option>
                                <option value="5">⭐⭐⭐⭐⭐ 大师</option>
                            </select>
                        </div>

                        <!-- 版本 -->
                        <div class="col-md-6 mb-3">
                            <label for="version" class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号
                            </label>
                            <input type="text" class="form-control" name="version" id="version" 
                                   th:value="${currentVersion}" readonly>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-flex justify-content-end gap-2">
                        <a th:href="@{/admin/resources}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i>
                            取消
                        </a>
                        <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始上传
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="alert alert-info mt-4">
            <h6><i class="fas fa-info-circle"></i> 使用说明</h6>
            <ul class="mb-0">
                <li><strong>选择文件</strong>：点击"选择文件"按钮选择多个文件，或点击"选择文件夹"选择整个文件夹</li>
                <li><strong>文件格式</strong>：支持JPG、PNG、GIF图片文件和JSON配置文件</li>
                <li><strong>自动分类</strong>：勾选后系统根据文件名关键词自动分类（如"animal"→动物分类）</li>
                <li><strong>文件配对</strong>：同名的图片和JSON文件会自动配对（如cat.jpg和cat.json）</li>
                <li><strong>拖拽上传</strong>：也可以直接将文件或文件夹拖拽到上传区域</li>
            </ul>
        </div>

        <style>
            .file-drop-zone {
                border: 3px dashed #007bff;
                border-radius: 10px;
                padding: 40px;
                text-align: center;
                background-color: #f8f9fa;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            .file-drop-zone:hover {
                border-color: #0056b3;
                background-color: #e3f2fd;
            }
            .file-drop-zone.dragover {
                border-color: #28a745;
                background-color: #d4edda;
            }
        </style>

        <script>
            let selectedFiles = [];

            // 格式化文件大小
            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // 删除文件
            function removeFile(index) {
                console.log('Removing file at index:', index);
                selectedFiles.splice(index, 1);
                updateFilesList();
                updateUploadButton();
            }

            // 切换分类模式
            function toggleCategoryMode() {
                const autoCategory = document.getElementById('autoCategory');
                const manualSection = document.getElementById('manualCategorySection');
                const categorySelect = document.getElementById('categorySelect');
                
                if (autoCategory.checked) {
                    manualSection.style.opacity = '0.5';
                    if (categorySelect) {
                        categorySelect.required = false;
                        categorySelect.disabled = true;
                    }
                } else {
                    manualSection.style.opacity = '1';
                    if (categorySelect) {
                        categorySelect.required = true;
                        categorySelect.disabled = false;
                    }
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                console.log('DOM loaded, initializing upload functionality');
                
                const dropZone = document.getElementById('dropZone');
                const filesInput = document.getElementById('filesInput');
                const folderInput = document.getElementById('folderInput');
                const filesList = document.getElementById('filesList');
                const filesContainer = document.getElementById('filesContainer');
                const uploadBtn = document.getElementById('uploadBtn');
                const selectFilesBtn = document.getElementById('selectFilesBtn');
                const selectFolderBtn = document.getElementById('selectFolderBtn');

                // 检查元素是否存在
                console.log('Elements found:', {
                    dropZone: !!dropZone,
                    filesInput: !!filesInput,
                    folderInput: !!folderInput,
                    filesList: !!filesList,
                    filesContainer: !!filesContainer,
                    uploadBtn: !!uploadBtn,
                    selectFilesBtn: !!selectFilesBtn,
                    selectFolderBtn: !!selectFolderBtn
                });

                // 选择文件按钮
                selectFilesBtn.addEventListener('click', function(e) {
                    e.preventDefault(); // 阻止默认行为
                    e.stopPropagation(); // 阻止事件冒泡
                    e.stopImmediatePropagation(); // 阻止同一元素上的其他监听器
                    console.log('Select files button clicked');
                    filesInput.click();
                });

                // 选择文件夹按钮
                selectFolderBtn.addEventListener('click', function(e) {
                    e.preventDefault(); // 阻止默认行为
                    e.stopPropagation(); // 阻止事件冒泡
                    e.stopImmediatePropagation(); // 阻止同一元素上的其他监听器
                    console.log('Select folder button clicked');
                    folderInput.click();
                });

                // 点击拖拽区域选择文件（避免与按钮冲突）
                dropZone.addEventListener('click', function(e) {
                    // 如果点击的是按钮或按钮内的元素，不触发文件选择
                    if (e.target.closest('button') || e.target.tagName === 'BUTTON') {
                        console.log('Button clicked, ignoring drop zone click');
                        return;
                    }
                    // 只有点击空白区域才触发文件选择
                    if (e.target === dropZone || (e.target.closest('.file-drop-zone') === dropZone && !e.target.closest('button'))) {
                        console.log('Drop zone clicked (not button)');
                        filesInput.click();
                    }
                });

                // 拖拽功能
                dropZone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    dropZone.classList.add('dragover');
                });

                dropZone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    dropZone.classList.remove('dragover');
                });

                dropZone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    dropZone.classList.remove('dragover');
                    
                    console.log('Drop event triggered');
                    
                    // 处理拖拽的文件和文件夹
                    if (e.dataTransfer.items) {
                        console.log('Processing dataTransfer items:', e.dataTransfer.items.length);
                        handleDroppedItems(e.dataTransfer.items);
                    } else {
                        console.log('Processing dataTransfer files:', e.dataTransfer.files.length);
                        const files = Array.from(e.dataTransfer.files);
                        handleFiles(files);
                    }
                });

                // 文件选择
                filesInput.addEventListener('change', function(e) {
                    console.log('Files input changed, files count:', e.target.files.length);
                    const files = Array.from(e.target.files);
                    handleFiles(files);
                });

                // 文件夹选择
                folderInput.addEventListener('change', function(e) {
                    console.log('Folder input changed, files count:', e.target.files.length);
                    const files = Array.from(e.target.files);
                    handleFiles(files);
                });

                // 处理拖拽的项目（包括文件夹）
                async function handleDroppedItems(items) {
                    const files = [];
                    
                    for (let i = 0; i < items.length; i++) {
                        const item = items[i];
                        if (item.kind === 'file') {
                            const entry = item.webkitGetAsEntry();
                            if (entry) {
                                if (entry.isFile) {
                                    const file = item.getAsFile();
                                    files.push(file);
                                } else if (entry.isDirectory) {
                                    console.log('Processing directory:', entry.name);
                                    const dirFiles = await readDirectory(entry);
                                    files.push(...dirFiles);
                                }
                            }
                        }
                    }
                    
                    console.log('Total files from drop:', files.length);
                    handleFiles(files);
                }

                // 递归读取文件夹
                function readDirectory(dirEntry) {
                    return new Promise((resolve) => {
                        const files = [];
                        const dirReader = dirEntry.createReader();
                        
                        function readEntries() {
                            dirReader.readEntries(async (entries) => {
                                if (entries.length === 0) {
                                    resolve(files);
                                    return;
                                }
                                
                                for (const entry of entries) {
                                    if (entry.isFile) {
                                        const file = await new Promise((resolve) => {
                                            entry.file(resolve);
                                        });
                                        files.push(file);
                                    } else if (entry.isDirectory) {
                                        const subFiles = await readDirectory(entry);
                                        files.push(...subFiles);
                                    }
                                }
                                
                                readEntries(); // 继续读取更多条目
                            });
                        }
                        
                        readEntries();
                    });
                }

                function handleFiles(files) {
                    console.log('handleFiles called with:', files.length, 'files');
                    
                    selectedFiles = files.filter(file => {
                        const isImage = file.type.startsWith('image/');
                        const isJson = file.name.toLowerCase().endsWith('.json');
                        console.log('File:', file.name, 'Type:', file.type, 'IsImage:', isImage, 'IsJson:', isJson);
                        return isImage || isJson;
                    });
                    
                    console.log('Filtered files:', selectedFiles.length);
                    updateFilesList();
                    updateUploadButton();
                }

                function updateFilesList() {
                    console.log('updateFilesList called, selectedFiles.length:', selectedFiles.length);
                    
                    if (selectedFiles.length === 0) {
                        filesList.style.display = 'none';
                        console.log('No files, hiding list');
                        return;
                    }

                    filesList.style.display = 'block';
                    filesContainer.innerHTML = '';
                    console.log('Showing file list');

                    selectedFiles.forEach((file, index) => {
                        console.log('Adding file to list:', file.name);
                        
                        const fileItem = document.createElement('div');
                        fileItem.className = 'd-flex justify-content-between align-items-center py-2 border-bottom';
                        
                        const isImage = file.type.startsWith('image/');
                        const icon = isImage ? 'fa-image text-primary' : 'fa-file-code text-info';
                        
                        fileItem.innerHTML = `
                            <div class="d-flex align-items-center">
                                <i class="fas ${icon} me-2"></i>
                                <div>
                                    <div class="fw-bold">${file.name}</div>
                                    <small class="text-muted">${formatFileSize(file.size)}</small>
                                </div>
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                    onclick="removeFile(${index})">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        filesContainer.appendChild(fileItem);
                    });
                }

                function updateUploadButton() {
                    const fileCount = selectedFiles.length;
                    uploadBtn.disabled = fileCount === 0;
                    
                    if (fileCount > 0) {
                        uploadBtn.innerHTML = `
                            <i class="fas fa-upload"></i>
                            上传 ${fileCount} 个文件
                        `;
                    } else {
                        uploadBtn.innerHTML = `
                            <i class="fas fa-upload"></i>
                            开始上传
                        `;
                    }
                }

                // 表单提交前处理
                document.querySelector('form').addEventListener('submit', function(e) {
                    console.log('Form submit triggered, selectedFiles.length:', selectedFiles.length);
                    
                    if (selectedFiles.length === 0) {
                        e.preventDefault();
                        alert('请选择要上传的文件');
                        return false;
                    }

                    // 创建新的DataTransfer对象来设置文件
                    const dt = new DataTransfer();
                    selectedFiles.forEach(file => {
                        dt.items.add(file);
                    });
                    
                    // 将文件设置到隐藏的input中
                    filesInput.files = dt.files;
                    console.log('Files set to input:', filesInput.files.length);
                    
                    return true;
                });
            });
        </script>
    </div>
</body>
</html>