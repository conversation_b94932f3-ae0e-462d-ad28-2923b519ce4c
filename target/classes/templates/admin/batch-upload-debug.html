<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>批量上传调试 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-bug"></i>
                批量上传功能调试
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools"></i>
                    文件选择功能测试
                </h5>
            </div>
            <div class="card-body">
                
                <!-- 浏览器兼容性检查 -->
                <div class="mb-4">
                    <h6>浏览器兼容性检查</h6>
                    <div id="browserCheck" class="alert alert-info">
                        <div id="browserInfo"></div>
                    </div>
                </div>

                <!-- 测试1：文件夹选择 -->
                <div class="mb-4">
                    <h6>测试1：文件夹选择</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <input type="file" id="folderInput1" class="d-none" webkitdirectory directory multiple>
                            <button type="button" class="btn btn-primary" onclick="testFolderSelect1()">
                                <i class="fas fa-folder"></i>
                                方法1：直接点击
                            </button>
                            <div id="result1" class="mt-2 small text-muted">未测试</div>
                        </div>
                        <div class="col-md-6">
                            <input type="file" id="folderInput2" class="d-none" webkitdirectory directory multiple>
                            <button type="button" class="btn btn-outline-primary" id="folderBtn2">
                                <i class="fas fa-folder-open"></i>
                                方法2：事件监听
                            </button>
                            <div id="result2" class="mt-2 small text-muted">未测试</div>
                        </div>
                    </div>
                </div>

                <!-- 测试2：普通文件选择 -->
                <div class="mb-4">
                    <h6>测试2：普通文件选择（对比测试）</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <input type="file" id="fileInput1" class="d-none" multiple accept="image/*,.json">
                            <button type="button" class="btn btn-success" onclick="testFileSelect1()">
                                <i class="fas fa-file"></i>
                                方法1：直接点击
                            </button>
                            <div id="fileResult1" class="mt-2 small text-muted">未测试</div>
                        </div>
                        <div class="col-md-6">
                            <input type="file" id="fileInput2" class="d-none" multiple accept="image/*,.json">
                            <button type="button" class="btn btn-outline-success" id="fileBtn2">
                                <i class="fas fa-files"></i>
                                方法2：事件监听
                            </button>
                            <div id="fileResult2" class="mt-2 small text-muted">未测试</div>
                        </div>
                    </div>
                </div>

                <!-- 测试3：点击区域 -->
                <div class="mb-4">
                    <h6>测试3：点击区域触发</h6>
                    <div class="border rounded p-3 text-center" 
                         style="cursor: pointer; background-color: #f8f9fa;" 
                         onclick="testAreaClick()">
                        <i class="fas fa-mouse-pointer fa-2x text-muted mb-2"></i>
                        <p class="mb-0">点击此区域测试文件夹选择</p>
                    </div>
                    <input type="file" id="areaFolderInput" class="d-none" webkitdirectory directory multiple>
                    <div id="areaResult" class="mt-2 small text-muted">未测试</div>
                </div>

                <!-- 文件列表显示 -->
                <div class="mb-4">
                    <h6>选择结果</h6>
                    <div id="filesList" class="border rounded p-3" style="min-height: 100px; background-color: #f8f9fa;">
                        <div class="text-muted text-center">请选择文件夹或文件</div>
                    </div>
                </div>

                <!-- 控制台日志 -->
                <div class="mb-4">
                    <h6>调试日志</h6>
                    <div id="debugLog" class="border rounded p-3" 
                         style="height: 200px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 12px;">
                    </div>
                </div>
            </div>
        </div>

        <!-- 实际上传表单 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    实际上传测试
                </h5>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label class="form-label">选择文件夹进行上传</label>
                        <input type="file" name="files" id="uploadFolderInput" class="form-control" 
                               webkitdirectory directory multiple>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">选择分类</label>
                        <select class="form-select" name="categoryId" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">难度等级</label>
                            <select class="form-select" name="difficulty" required>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">版本号</label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i>
                        开始上传
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 调试日志函数
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debugLog');
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // 显示文件列表
        function showFilesList(files, source) {
            const filesDiv = document.getElementById('filesList');
            if (files.length === 0) {
                filesDiv.innerHTML = '<div class="text-muted text-center">未选择文件</div>';
                return;
            }

            let html = `<strong>${source}: 已选择 ${files.length} 个文件</strong><br>`;
            const imageFiles = Array.from(files).filter(f => /\.(png|jpg|jpeg|gif)$/i.test(f.name));
            const jsonFiles = Array.from(files).filter(f => /\.json$/i.test(f.name));
            
            html += `<small>图片文件: ${imageFiles.length} 个，JSON文件: ${jsonFiles.length} 个</small><br>`;
            
            // 显示前5个文件
            Array.from(files).slice(0, 5).forEach(file => {
                const icon = file.name.toLowerCase().endsWith('.json') ? 'fa-file-code' : 'fa-file-image';
                html += `<div class="small"><i class="fas ${icon} me-1"></i>${file.name}</div>`;
            });
            
            if (files.length > 5) {
                html += `<div class="small text-muted">... 还有 ${files.length - 5} 个文件</div>`;
            }

            filesDiv.innerHTML = html;
        }

        // 测试函数
        function testFolderSelect1() {
            debugLog('测试1：直接点击文件夹选择');
            try {
                document.getElementById('folderInput1').click();
                debugLog('测试1：文件夹选择对话框已触发');
            } catch (error) {
                debugLog('测试1：错误 - ' + error.message);
            }
        }

        function testFileSelect1() {
            debugLog('测试：直接点击文件选择');
            try {
                document.getElementById('fileInput1').click();
                debugLog('测试：文件选择对话框已触发');
            } catch (error) {
                debugLog('测试：错误 - ' + error.message);
            }
        }

        function testAreaClick() {
            debugLog('测试3：区域点击触发文件夹选择');
            try {
                document.getElementById('areaFolderInput').click();
                debugLog('测试3：文件夹选择对话框已触发');
            } catch (error) {
                debugLog('测试3：错误 - ' + error.message);
            }
        }

        // 页面加载完成后设置
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面加载完成，开始初始化');

            // 浏览器兼容性检查
            const supportsDirectory = 'webkitdirectory' in document.createElement('input');
            const browserInfo = document.getElementById('browserInfo');
            browserInfo.innerHTML = `
                <strong>文件夹选择支持:</strong> ${supportsDirectory ? '✅ 支持' : '❌ 不支持'}<br>
                <strong>浏览器:</strong> ${navigator.userAgent}<br>
                <strong>平台:</strong> ${navigator.platform}
            `;

            if (!supportsDirectory) {
                document.getElementById('browserCheck').className = 'alert alert-danger';
                debugLog('警告：当前浏览器不支持文件夹选择功能');
            } else {
                debugLog('浏览器支持文件夹选择功能');
            }

            // 设置事件监听器
            document.getElementById('folderBtn2').addEventListener('click', function() {
                debugLog('测试2：事件监听器触发文件夹选择');
                try {
                    document.getElementById('folderInput2').click();
                    debugLog('测试2：文件夹选择对话框已触发');
                } catch (error) {
                    debugLog('测试2：错误 - ' + error.message);
                }
            });

            document.getElementById('fileBtn2').addEventListener('click', function() {
                debugLog('测试：事件监听器触发文件选择');
                try {
                    document.getElementById('fileInput2').click();
                    debugLog('测试：文件选择对话框已触发');
                } catch (error) {
                    debugLog('测试：错误 - ' + error.message);
                }
            });

            // 文件选择变化监听
            document.getElementById('folderInput1').addEventListener('change', function(e) {
                debugLog(`测试1结果：选择了 ${e.target.files.length} 个文件`);
                document.getElementById('result1').innerHTML = `<span class="text-success">成功选择 ${e.target.files.length} 个文件</span>`;
                showFilesList(e.target.files, '测试1');
            });

            document.getElementById('folderInput2').addEventListener('change', function(e) {
                debugLog(`测试2结果：选择了 ${e.target.files.length} 个文件`);
                document.getElementById('result2').innerHTML = `<span class="text-success">成功选择 ${e.target.files.length} 个文件</span>`;
                showFilesList(e.target.files, '测试2');
            });

            document.getElementById('fileInput1').addEventListener('change', function(e) {
                debugLog(`文件测试1结果：选择了 ${e.target.files.length} 个文件`);
                document.getElementById('fileResult1').innerHTML = `<span class="text-success">成功选择 ${e.target.files.length} 个文件</span>`;
                showFilesList(e.target.files, '文件测试1');
            });

            document.getElementById('fileInput2').addEventListener('change', function(e) {
                debugLog(`文件测试2结果：选择了 ${e.target.files.length} 个文件`);
                document.getElementById('fileResult2').innerHTML = `<span class="text-success">成功选择 ${e.target.files.length} 个文件</span>`;
                showFilesList(e.target.files, '文件测试2');
            });

            document.getElementById('areaFolderInput').addEventListener('change', function(e) {
                debugLog(`测试3结果：选择了 ${e.target.files.length} 个文件`);
                document.getElementById('areaResult').innerHTML = `<span class="text-success">成功选择 ${e.target.files.length} 个文件</span>`;
                showFilesList(e.target.files, '测试3');
            });

            document.getElementById('uploadFolderInput').addEventListener('change', function(e) {
                debugLog(`上传表单：选择了 ${e.target.files.length} 个文件`);
            });

            debugLog('所有事件监听器设置完成');
        });
    </script>
</body>
</html>
