<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>分类管理 - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-tags"></i>
                分类管理
            </h2>
            <div class="btn-group">
                <a th:href="@{/admin/categories/create}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    创建分类
                </a>
                <button type="button" class="btn btn-warning" onclick="fixCategoryData()">
                    <i class="fas fa-tools"></i>
                    修复分类数据
                </button>
                <button type="button" class="btn btn-info" onclick="getCategoryStatistics()">
                    <i class="fas fa-chart-bar"></i>
                    查看统计
                </button>
            </div>
        </div>

        <!-- 分类统计 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tags fa-2x mb-2"></i>
                        <h4 th:text="${categories.size()}">0</h4>
                        <p class="mb-0">总分类数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-check-circle fa-2x mb-2"></i>
                        <h4 th:text="${#lists.size(categories.?[isActive == true])}">0</h4>
                        <p class="mb-0">激活分类</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-pause-circle fa-2x mb-2"></i>
                        <h4 th:text="${#lists.size(categories.?[isActive == false])}">0</h4>
                        <p class="mb-0">禁用分类</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-globe fa-2x mb-2"></i>
                        <h4 th:text="${#lists.size(categories.?[nameEn != null and nameEn != ''])}">0</h4>
                        <p class="mb-0">已国际化</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i>
                    分类列表
                </h5>
            </div>
            <div class="card-body">
                <div th:if="${#lists.isEmpty(categories)}" class="text-center py-5">
                    <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">暂无分类</h5>
                    <p class="text-muted">点击上方"创建分类"按钮添加第一个分类</p>
                </div>

                <div th:if="${!#lists.isEmpty(categories)}" class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>排序</th>
                                <th>图标</th>
                                <th>中文名称</th>
                                <th>英文名称</th>
                                <th>描述</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="category : ${categories}">
                                <td>
                                    <span class="badge bg-secondary" th:text="${category.sortOrder}">0</span>
                                </td>
                                <td>
                                    <div th:if="${category.iconUrl != null and category.iconUrl != ''}">
                                        <img th:src="${category.iconUrl}" alt="分类图标" 
                                             class="rounded" style="width: 32px; height: 32px; object-fit: cover;">
                                    </div>
                                    <div th:unless="${category.iconUrl != null and category.iconUrl != ''}">
                                        <i class="fas fa-image text-muted" style="font-size: 24px;"></i>
                                    </div>
                                </td>
                                <td>
                                    <strong th:text="${category.name}">分类名称</strong>
                                </td>
                                <td>
                                    <span th:if="${category.nameEn != null and category.nameEn != ''}" 
                                          class="badge bg-info" th:text="${category.nameEn}">English Name</span>
                                    <span th:unless="${category.nameEn != null and category.nameEn != ''}" 
                                          class="text-muted">未设置</span>
                                </td>
                                <td>
                                    <span th:if="${category.description != null and category.description != ''}" 
                                          th:text="${#strings.abbreviate(category.description, 50)}">描述</span>
                                    <span th:unless="${category.description != null and category.description != ''}" 
                                          class="text-muted">无描述</span>
                                </td>
                                <td>
                                    <span th:if="${category.isActive}" class="badge bg-success">
                                        <i class="fas fa-check"></i> 激活
                                    </span>
                                    <span th:unless="${category.isActive}" class="badge bg-danger">
                                        <i class="fas fa-times"></i> 禁用
                                    </span>
                                </td>
                                <td>
                                    <small class="text-muted" 
                                           th:text="${#temporals.format(category.createdAt, 'yyyy-MM-dd HH:mm')}">
                                        2024-01-01 12:00
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a th:href="@{/admin/categories/{id}/edit(id=${category.id})}" 
                                           class="btn btn-sm btn-outline-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form th:action="@{/admin/categories/{id}/delete(id=${category.id})}" 
                                              method="post" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                    title="删除" onclick="return confirmDelete('确定要删除这个分类吗？')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 操作提示 -->
        <div class="mt-4">
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <strong>操作提示：</strong>
                <ul class="mb-0 mt-2">
                    <li>分类按排序顺序显示，数字越小排序越靠前</li>
                    <li>英文名称用于API通信，建议为所有分类设置英文名称</li>
                    <li>删除分类为软删除，不会真正删除数据</li>
                    <li>禁用的分类不会在客户端API中返回</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function fixCategoryData() {
            if (confirm('确定要修复分类数据吗？这将会：\n1. 确保所有分类都有正确的英文名称\n2. 修复资源分类关联错误\n3. 清理数据不一致问题\n\n请确认继续？')) {
                // 创建表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '/admin/fix-category-data';

                // 添加CSRF token
                const csrfToken = document.querySelector('meta[name="_csrf"]');
                if (csrfToken) {
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = '_csrf';
                    csrfInput.value = csrfToken.getAttribute('content');
                    form.appendChild(csrfInput);
                }

                document.body.appendChild(form);
                form.submit();
            }
        }

        function getCategoryStatistics() {
            fetch('/admin/category-statistics')
                .then(response => response.text())
                .then(data => {
                    alert('分类统计信息：\n' + data + '\n\n详细信息请查看服务器日志');
                })
                .catch(error => {
                    alert('获取统计信息失败: ' + error);
                });
        }
    </script>
</body>
</html>