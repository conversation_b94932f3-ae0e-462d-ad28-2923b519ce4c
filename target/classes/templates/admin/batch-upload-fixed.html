<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: layout(~{::title}, ~{::content})}">
<head>
    <title>批量上传（修复版） - Dreaming涂色App管理后台</title>
</head>
<body>
    <div th:fragment="content">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-folder-open"></i>
                批量上传（修复版）
            </h2>
            <a th:href="@{/admin/resources/list}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i>
                查看资源列表
            </a>
        </div>

        <!-- 浏览器兼容性警告区域 -->
        <div id="browserWarning" style="display: none;"></div>

        <!-- 成功消息 -->
        <div th:if="${successMessage}" class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i>
            <span th:text="${successMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 警告消息 -->
        <div th:if="${warningMessages}" class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>注意事项：</strong>
            <ul class="mb-0 mt-2">
                <li th:each="warning : ${warningMessages}" th:text="${warning}"></li>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle"></i>
            <span th:text="${errorMessage}"></span>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-plus"></i>
                    文件夹批量上传
                </h5>
                <small class="text-muted">支持选择整个文件夹进行批量上传</small>
            </div>
            <div class="card-body">
                <form action="/admin/resources/batch-upload" method="post" enctype="multipart/form-data" id="uploadForm">
                    
                    <!-- 文件选择区域 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-folder"></i>
                            选择文件夹或文件 <span class="text-danger">*</span>
                        </label>
                        
                        <!-- 选择按钮组 -->
                        <div class="btn-group w-100 mb-3" role="group">
                            <button type="button" class="btn btn-primary" onclick="selectFolder()" id="folderBtn">
                                <i class="fas fa-folder"></i>
                                选择文件夹
                            </button>
                            <button type="button" class="btn btn-outline-primary" onclick="selectFiles()">
                                <i class="fas fa-files"></i>
                                选择文件
                            </button>
                        </div>
                        
                        <!-- 隐藏的文件输入 -->
                        <input type="file" id="folderInput" name="files" class="d-none" 
                               webkitdirectory directory multiple>
                        <input type="file" id="filesInput" class="d-none" 
                               accept="image/*,.json" multiple>
                        
                        <!-- 拖拽区域 -->
                        <div class="border rounded p-4 text-center mb-3" 
                             style="border: 2px dashed #dee2e6; background-color: #f8f9fa; cursor: pointer;" 
                             id="dropZone">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>或拖拽文件夹/文件到此处</h5>
                            <p class="text-muted mb-0">
                                支持拖拽整个文件夹或多个文件
                            </p>
                        </div>
                        
                        <!-- 文件列表显示 -->
                        <div id="filesList" class="mt-3" style="display: none;">
                            <div class="alert alert-light">
                                <h6><i class="fas fa-list"></i> 已选择的文件：</h6>
                                <div id="filesContainer"></div>
                                <div id="pairingStatus" class="mt-2"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分类选择 -->
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="fas fa-tags"></i>
                            选择分类 <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" name="categoryId" required>
                            <option value="">请选择分类</option>
                            <option th:each="category : ${categoryDTOs}" 
                                    th:value="${category.id}" 
                                    th:text="${category.name + ' (' + category.nameEn + ')'}">分类名称</option>
                        </select>
                    </div>

                    <div class="row">
                        <!-- 难度等级 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-star"></i>
                                难度等级 <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" name="difficulty" required>
                                <option value="">请选择难度</option>
                                <option value="1">⭐ 简单</option>
                                <option value="2">⭐⭐ 容易</option>
                                <option value="3" selected>⭐⭐⭐ 中等</option>
                                <option value="4">⭐⭐⭐⭐ 困难</option>
                                <option value="5">⭐⭐⭐⭐⭐ 专家</option>
                            </select>
                        </div>

                        <!-- 版本号 -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-code-branch"></i>
                                版本号 <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" name="version" 
                                   th:value="${currentVersion}" required>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-success" id="submitBtn" disabled>
                            <i class="fas fa-upload"></i>
                            开始批量上传
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle"></i>
                    使用说明
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-folder"></i> 文件夹上传（推荐）</h6>
                        <ul class="small">
                            <li>点击"选择文件夹"按钮</li>
                            <li>选择包含图片和JSON文件的文件夹</li>
                            <li>系统自动读取所有子文件</li>
                            <li>自动配对同名的图片和JSON文件</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-browser"></i> 浏览器要求</h6>
                        <ul class="small">
                            <li>Chrome 21+ ✅ 推荐</li>
                            <li>Firefox 50+ ✅ 推荐</li>
                            <li>Edge 14+ ✅ 推荐</li>
                            <li>Safari 11.1+ ⚠️ 部分支持</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];

        // 选择文件夹
        function selectFolder() {
            console.log('选择文件夹');
            const folderInput = document.getElementById('folderInput');
            
            // 检查浏览器支持
            if (!('webkitdirectory' in folderInput)) {
                alert('当前浏览器不支持文件夹选择功能\n请使用 Chrome、Firefox 或 Edge 浏览器');
                return;
            }
            
            try {
                folderInput.click();
                console.log('文件夹选择对话框已触发');
            } catch (error) {
                console.error('文件夹选择失败:', error);
                alert('文件夹选择失败: ' + error.message);
            }
        }

        // 选择文件
        function selectFiles() {
            console.log('选择文件');
            try {
                document.getElementById('filesInput').click();
                console.log('文件选择对话框已触发');
            } catch (error) {
                console.error('文件选择失败:', error);
                alert('文件选择失败: ' + error.message);
            }
        }

        // 处理文件选择
        function handleFiles(files, source) {
            console.log(`${source}: 选择了 ${files.length} 个文件`);
            selectedFiles = Array.from(files);
            displayFiles();
            updateSubmitButton();
            
            // 将文件设置到表单的主输入框
            const formInput = document.getElementById('folderInput');
            if (formInput && files.length > 0) {
                const dt = new DataTransfer();
                Array.from(files).forEach(file => dt.items.add(file));
                formInput.files = dt.files;
            }
        }

        // 显示文件列表
        function displayFiles() {
            const filesList = document.getElementById('filesList');
            const filesContainer = document.getElementById('filesContainer');
            
            if (selectedFiles.length === 0) {
                filesList.style.display = 'none';
                return;
            }

            filesList.style.display = 'block';
            
            // 分类显示文件
            const imageFiles = selectedFiles.filter(f => /\.(png|jpg|jpeg|gif)$/i.test(f.name));
            const jsonFiles = selectedFiles.filter(f => /\.json$/i.test(f.name));
            
            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <strong>图片文件 (${imageFiles.length}个):</strong>
                        <div class="small mt-1">
            `;
            
            imageFiles.slice(0, 5).forEach(file => {
                html += `<div><i class="fas fa-image text-primary me-1"></i>${file.name}</div>`;
            });
            
            if (imageFiles.length > 5) {
                html += `<div class="text-muted">... 还有 ${imageFiles.length - 5} 个图片文件</div>`;
            }
            
            html += `
                        </div>
                    </div>
                    <div class="col-md-6">
                        <strong>JSON文件 (${jsonFiles.length}个):</strong>
                        <div class="small mt-1">
            `;
            
            jsonFiles.slice(0, 5).forEach(file => {
                html += `<div><i class="fas fa-file-code text-success me-1"></i>${file.name}</div>`;
            });
            
            if (jsonFiles.length > 5) {
                html += `<div class="text-muted">... 还有 ${jsonFiles.length - 5} 个JSON文件</div>`;
            }
            
            html += `
                        </div>
                    </div>
                </div>
            `;

            filesContainer.innerHTML = html;
            validateFilePairing();
        }

        // 验证文件配对
        function validateFilePairing() {
            const pairingStatus = document.getElementById('pairingStatus');
            const imageFiles = new Map();
            const jsonFiles = new Map();
            
            selectedFiles.forEach(file => {
                const baseName = file.name.replace(/\.[^/.]+$/, "");
                if (/\.(png|jpg|jpeg|gif)$/i.test(file.name)) {
                    imageFiles.set(baseName, file);
                } else if (/\.json$/i.test(file.name)) {
                    jsonFiles.set(baseName, file);
                }
            });

            let pairedCount = 0;
            let warnings = [];

            imageFiles.forEach((file, baseName) => {
                if (jsonFiles.has(baseName)) {
                    pairedCount++;
                } else {
                    warnings.push(`图片 ${file.name} 缺少对应的JSON文件`);
                }
            });

            jsonFiles.forEach((file, baseName) => {
                if (!imageFiles.has(baseName)) {
                    warnings.push(`JSON文件 ${file.name} 缺少对应的图片文件`);
                }
            });

            let statusHtml = `<strong>配对状态：</strong> ${pairedCount} 个有效文件对`;
            if (warnings.length > 0) {
                statusHtml += `<div class="text-warning mt-2"><i class="fas fa-exclamation-triangle"></i> 警告 (${warnings.length}个):</div>`;
                statusHtml += '<div class="small">';
                warnings.slice(0, 3).forEach(warning => {
                    statusHtml += `<div>• ${warning}</div>`;
                });
                if (warnings.length > 3) {
                    statusHtml += `<div class="text-muted">... 还有 ${warnings.length - 3} 个警告</div>`;
                }
                statusHtml += '</div>';
            }

            pairingStatus.innerHTML = statusHtml;
        }

        // 更新提交按钮
        function updateSubmitButton() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = selectedFiles.length === 0;
        }

        // 页面加载完成后设置
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 检查浏览器兼容性
            const supportsDirectory = 'webkitdirectory' in document.createElement('input');
            console.log('浏览器支持文件夹选择:', supportsDirectory);
            
            if (!supportsDirectory) {
                const warningDiv = document.getElementById('browserWarning');
                warningDiv.innerHTML = `
                    <div class="alert alert-warning alert-dismissible fade show">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>浏览器兼容性警告：</strong> 当前浏览器不支持文件夹选择功能。
                        请使用 <strong>Chrome、Firefox 或 Edge</strong> 浏览器以获得最佳体验。
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                warningDiv.style.display = 'block';
                
                // 禁用文件夹选择按钮
                document.getElementById('folderBtn').disabled = true;
                document.getElementById('folderBtn').innerHTML = '<i class="fas fa-times"></i> 不支持文件夹选择';
            }

            // 文件夹选择监听
            document.getElementById('folderInput').addEventListener('change', function(e) {
                handleFiles(e.target.files, '文件夹选择');
            });

            // 文件选择监听
            document.getElementById('filesInput').addEventListener('change', function(e) {
                handleFiles(e.target.files, '文件选择');
            });

            // 拖拽功能
            const dropZone = document.getElementById('dropZone');
            
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#0d6efd';
                dropZone.style.backgroundColor = '#f0f8ff';
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.style.borderColor = '#dee2e6';
                dropZone.style.backgroundColor = '#f8f9fa';
                handleFiles(e.dataTransfer.files, '拖拽');
            });

            // 表单提交验证
            document.getElementById('uploadForm').addEventListener('submit', function(e) {
                if (selectedFiles.length === 0) {
                    alert('请选择要上传的文件夹或文件');
                    e.preventDefault();
                    return;
                }

                const submitBtn = document.getElementById('submitBtn');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
                submitBtn.disabled = true;
            });

            console.log('初始化完成');
        });
    </script>
</body>
</html>
